.modal-container {

  .block {
    padding-top: 20px;
  }

  &.breakpoint-modal {

    .block {
      padding-top: 30px;
    }

    .modal-header {
      margin-bottom: 16px;
    }
  }
}

.modal-header {
  padding: 8px 24px;
  color: var(--color-background-secondary-white-600);
}

.hero-block {
  border-radius: 0 0 var(--radius-large) var(--radius-large);
  background: linear-gradient(180deg, #0A0A0A, var(--color-background-primary-black-600));
  backdrop-filter: blur(8px);
}

.title-container {

  ion-icon {
    font-size: 24px;
    padding: 6px;
    margin-right: 6px;
  }

  .titles {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }


  .title {
    font-size: 20px;
    font-style: normal;
    font-weight: 300;
    line-height: normal;
    padding: 0;
    display: block;
    white-space: normal !important;
    text-align: start;
  }

  .subtitle {
    font-size: 16px;
    font-weight: 300;
    color: var(--color-background-primary-black-200);
  }
}

.close-btn {
  font-size: 32px;
  cursor: pointer;
}

ion-content {
  height: calc(100vh - 96px);
  --padding-bottom: 20px;
}

.footer {
  max-width: 100vw;
  margin: 0 auto;
  z-index: 100;
  padding-top: 20px;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;

  &.visible {
    opacity: 1;
  }

  &.fixed {
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: calc(env(safe-area-inset-bottom) - 10px);
    padding-bottom: 44px;
    width: 100%;
    padding-top: 16px;
    background-color: var(--color-background-primary-black-700);

    mpg-button {
      width: calc(100% - 60px);
    }
  }

  mpg-button, .secondary {
    width: 100%;
    max-width: 500px;

    ion-icon {
      font-size: 20px;
    }
  }

  .secondary {
    color: var(--color-accent-primary-red-800);
    text-align: center;
    padding: 16px;
  }
}

.sticky-content {
  padding-right: 30px;
  padding-left: 30px;
}
