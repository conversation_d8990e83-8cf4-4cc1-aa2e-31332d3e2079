.set {
  border-radius: var(--radius-medium-small);
  background: var(--color-background-primary-black-500);
  margin: 16px 0;
  padding: 8px;


  ion-label {
    font-size: 18px;
    margin-left: 10px;
    margin-right: 10px;
  }

  mpg-loading-checkmark {
    margin-top: 2px;
    margin-left: 5px;
  }

  ion-input {
    width: 75px;
    height: 20px !important;
    text-align: center;
    --padding-start: 0 !important;
    --background: var(--color-background-primary-black-500) !important;
    --border-radius: var(--radius-medium) !important;
  }

  ion-icon {
    font-size: 24px;
    display: block;
    --color: var(--color-background-secondary-white-900);
  }

  &:last-of-type {
    margin-bottom: 0;
  }
}
