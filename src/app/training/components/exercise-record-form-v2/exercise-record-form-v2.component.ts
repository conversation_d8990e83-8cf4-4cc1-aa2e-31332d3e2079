import { Component, Input, OnInit } from '@angular/core';
import {
  WorkoutExerciseRecord,
  WorkoutExerciseSetRecord,
  WorkoutRecord,
} from '../../models';
import { StorageObjectInProgress } from '../../../shared/models';
import { FormGroup } from '@angular/forms';
import { environment } from '../../../../environments/environment';
import { of, switchMap } from 'rxjs';
import {
  WorkoutExerciseSetRecordService,
  WorkoutRecordService,
} from '../../services';
import { FileService, ToastService } from '../../../shared/services';

@Component({
  selector: 'mpg-exercise-record-form-v2',
  templateUrl: './exercise-record-form-v2.component.html',
  styleUrls: ['./exercise-record-form-v2.component.scss'],
})
export class ExerciseRecordFormV2Component implements OnInit {
  @Input() workoutRecord: WorkoutRecord;
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @Input() setRecordsFormGroupMap: Record<string, FormGroup>;
  @Input() setRecordsLoadingMap: Record<string, boolean>;
  @Input() loadingCallback?: () => void;
  @Input() finishedCallback?: () => void;
  @Input() color: 'primary' | 'secondary' = 'primary';

  selectedSetRecord: WorkoutExerciseSetRecord;
  setRecordVideo: StorageObjectInProgress;

  constructor(
    private workoutRecordService: WorkoutRecordService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
    private fileService: FileService,
    private toastService: ToastService,
  ) {}

  ngOnInit(): void {}

  handleVideoSelect(event: any) {
    if (this.loadingCallback) {
      this.loadingCallback();
    }

    this.fileService
      .uploadFile(
        event,
        this.workoutRecordService.getSetRecordVideoUploadUrl(
          this.workoutRecord.id,
          this.selectedSetRecord.exerciseRecord.id,
          this.selectedSetRecord.id,
        ),
        environment.TRAINING_SERVICE_API_URL,
      )
      .pipe(
        switchMap((storageObject) => {
          if (storageObject.progress < 100) {
            return of(storageObject);
          }

          return this.workoutRecordService.putSetRecordVideo(
            this.workoutRecord.id,
            this.selectedSetRecord.exerciseRecord.id,
            this.selectedSetRecord.id,
            { videoId: storageObject.id },
          );
        }),
      )
      .subscribe((res) => {
        if ('progress' in res) {
          this.setRecordVideo = res;
          return;
        }

        this.setRecordVideo = undefined;
        this.selectedSetRecord.video = res;
        this.selectedSetRecord = undefined;
        event.target.value = '';
        this.toastService.showInfoToast('video-upload-success');
        if (this.finishedCallback) {
          this.finishedCallback();
        }
      });
  }

  handleSetRecordVideoClick(setRecord: WorkoutExerciseSetRecord) {
    this.workoutExerciseSetRecordService.fetchAndShowSetRecordVideoModal({
      setRecordId: setRecord.id,
      onDelete: () => {
        setRecord.video = undefined;
      },
    });
  }
}
