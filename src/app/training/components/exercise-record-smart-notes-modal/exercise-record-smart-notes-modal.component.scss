// Override modal-layout-v2 styles to match Figma
:host ::ng-deep mpg-modal-layout-v2 .modal-container {
  background: rgba(7, 7, 7, 1);
  border-radius: 16px;
  width: 430px;
  max-width: 430px;
}

:host ::ng-deep mpg-modal-layout-v2 .modal-header {
  padding: 8px 0;
  gap: 89px;
}

:host ::ng-deep mpg-modal-layout-v2 .title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

:host ::ng-deep mpg-modal-layout-v2 .modal-title {
  color: rgba(244, 244, 255, 1);
  font-size: 22px;
  font-weight: 300;
  line-height: normal;
  text-align: left;
}

:host ::ng-deep mpg-modal-layout-v2 .modal-subtitle {
  color: rgba(102, 102, 102, 1);
  font-size: 15px;
  font-weight: 300;
  line-height: normal;
  text-align: center;
}

.chat-container {
  height: 622px;
  display: flex;
  flex-direction: column;
  background: rgba(2, 2, 2, 1);
  border-radius: 47px;
  padding: 0 24px 59px;
  gap: 16px;
  overflow: hidden;
}

.messages-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
  align-self: stretch;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  align-self: stretch;

  &.current-user {
    align-items: flex-end;
  }
}

.message-info {
  display: flex;
  align-items: center;
  gap: 4px;

  &.right-align {
    justify-content: flex-end;
  }

  .sender-name {
    color: rgba(191, 191, 191, 1);
    font-size: 14px;
    font-weight: 300;
    line-height: normal;
  }

  .timestamp {
    color: rgba(77, 76, 76, 1);
    font-size: 14px;
    font-weight: 300;
    line-height: normal;
  }

  &.current-user .timestamp {
    color: rgba(102, 102, 102, 1);
  }
}

// Media message (video thumbnail)
.media-message {
  background: rgba(30, 30, 30, 1);
  width: 90px;
  height: 160px;
  border-radius: 16px 6px 16px 16px;
  position: relative;
  overflow: hidden;
  cursor: pointer;

  .media-thumbnail {
    width: 100%;
    height: 100%;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .play-overlay {
      position: absolute;
      top: 0;
      left: 50.75px;
      width: 39px;
      padding: 10px;
      display: flex;
      align-items: center;
      gap: 10px;

      .play-icon {
        width: 19.25px;
        height: 14px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

// Voice message bubble
.voice-message {
  background: rgba(30, 30, 30, 1);
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 24px 24px 24px 6px;
  align-self: stretch;

  .play-button {
    background: rgba(242, 242, 242, 1);
    width: 24px;
    height: 24px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4px 8px;
    gap: 4px;
    flex-shrink: 0;
    position: relative;

    .play-icon {
      width: 20px;
      height: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 0;
    }

    .recording-dot {
      width: 4px;
      height: 4px;
      position: absolute;
      top: 10px;
      left: 10px;
      z-index: 1;
    }
  }
}

// Text message styles
.text-message {
  background: rgba(30, 30, 30, 1);
  padding: 8px 16px;
  border-radius: 24px 24px 24px 6px;

  ion-text {
    color: rgba(244, 244, 255, 1);
    font-size: 14px;
    font-weight: 300;
    line-height: normal;
  }
}

// Message input footer (from Figma design)
.message-input-container {
  background: rgba(7, 7, 7, 1);
  padding: 16px 24px;
  border-top: 1px solid rgba(30, 30, 30, 1);

  .input-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(30, 30, 30, 1);
    border-radius: 24px;
    padding: 8px 16px;

    .attachment-button,
    .voice-button {
      background: transparent;
      color: rgba(191, 191, 191, 1);
      width: 24px;
      height: 24px;
      margin: 0;
      padding: 0;

      ion-icon {
        font-size: 20px;
      }

      &.recording {
        color: rgba(255, 59, 48, 1);
        animation: pulse 1s infinite;
      }
    }

    .message-input {
      flex: 1;
      background: transparent;
      color: rgba(244, 244, 255, 1);
      font-size: 14px;
      font-weight: 300;
      border: none;
      outline: none;

      &::placeholder {
        color: rgba(102, 102, 102, 1);
      }
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
