.chat-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #1a1a1a;
}

.messages-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  &.current-user {
    align-items: flex-end;
  }
}

.message-bubble {
  max-width: 80%;
  background: #2d2d2d;
  border-radius: 16px;
  padding: 12px 16px;
  position: relative;

  &.current-user {
    background: #007aff;
    align-self: flex-end;
  }
}

.text-message {
  ion-text {
    color: #ffffff;
    font-size: 14px;
    line-height: 1.4;
  }
}

.voice-message {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  min-width: 200px;

  .play-button {
    --color: #ffffff;
    --padding-start: 8px;
    --padding-end: 8px;
    margin: 0;
    height: 32px;
    width: 32px;
  }

  .voice-waveform {
    flex: 1;
    height: 20px;
    display: flex;
    align-items: center;

    .waveform-bars {
      display: flex;
      align-items: center;
      gap: 2px;
      height: 100%;

      .bar {
        width: 3px;
        background: #ffffff;
        border-radius: 1px;
        opacity: 0.7;
        transition: opacity 0.2s ease;
      }
    }
  }

  .duration {
    color: #ffffff;
    font-size: 12px;
    opacity: 0.8;
  }

  &:hover .waveform-bars .bar {
    opacity: 1;
  }
}

.voice-transcript {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);

  ion-text {
    color: #ffffff;
    font-size: 12px;
    opacity: 0.8;
  }
}

.media-message {
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  position: relative;

  .media-thumbnail {
    position: relative;
    width: 200px;
    height: 150px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .media-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s ease;

      .preview-button {
        --color: #ffffff;
        --background: rgba(255, 255, 255, 0.2);
        --border-radius: 50%;
        height: 48px;
        width: 48px;
        margin-bottom: 8px;
      }

      .preview-text {
        color: #ffffff;
        font-size: 12px;
        font-weight: 500;
      }
    }

    &:hover .media-overlay {
      opacity: 1;
    }
  }
}

.message-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;

  .sender-name {
    color: #007aff;
    font-size: 11px;
    font-weight: 500;
  }

  .timestamp {
    color: #8e8e93;
    font-size: 11px;
  }
}

.message-input-container {
  background: #1a1a1a;
  border-top: 1px solid #2d2d2d;
  padding: 12px 16px;

  .input-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #2d2d2d;
    border-radius: 20px;
    padding: 4px;

    .attachment-button,
    .voice-button {
      --color: #8e8e93;
      --padding-start: 8px;
      --padding-end: 8px;
      margin: 0;
      height: 36px;
      width: 36px;

      &.recording {
        --color: #ff3b30;
        animation: pulse 1s infinite;
      }
    }

    .message-input {
      flex: 1;
      --background: transparent;
      --color: #ffffff;
      --placeholder-color: #8e8e93;
      --padding-start: 12px;
      --padding-end: 12px;
      font-size: 14px;
    }
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}
