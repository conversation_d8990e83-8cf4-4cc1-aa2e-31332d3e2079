.smart-notes-container {
  padding: 16px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-primary);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  
  .empty-icon {
    font-size: 64px;
    color: var(--ion-color-medium);
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
    font-weight: 500;
    color: var(--ion-color-dark);
    margin-bottom: 8px;
  }
  
  .empty-subtitle {
    font-size: 14px;
    color: var(--ion-color-medium);
  }
}

.notes-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.note-item {
  background: var(--ion-color-light);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--ion-color-light-shade);
  
  .note-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    .note-creator {
      font-weight: 600;
      color: var(--ion-color-primary);
      font-size: 14px;
    }
    
    .note-date {
      font-size: 12px;
      color: var(--ion-color-medium);
    }
  }
  
  .note-content {
    margin-bottom: 12px;
    
    ion-text {
      font-size: 14px;
      line-height: 1.5;
      color: var(--ion-color-dark);
    }
  }
  
  .note-media {
    margin-bottom: 12px;
    
    img {
      width: 100%;
      max-width: 200px;
      height: auto;
      border-radius: 8px;
    }
  }
  
  .note-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    
    ion-button {
      --padding-start: 8px;
      --padding-end: 8px;
      height: 32px;
    }
  }
}
