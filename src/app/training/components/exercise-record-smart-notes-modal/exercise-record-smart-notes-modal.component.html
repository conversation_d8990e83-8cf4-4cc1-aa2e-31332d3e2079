<mpg-modal-layout
  [button]="modalButton"
  [title]="title"
>
  <div class="smart-notes-container">
    <div class="header-section">
      <ion-text class="section-title">
        {{ 'training.smart-notes' | translate }}
      </ion-text>
      <ion-button 
        fill="outline" 
        size="small"
        (click)="handleCreateSmartNote()"
      >
        <ion-icon name="add" slot="start"></ion-icon>
        {{ 'buttons.add-note' | translate }}
      </ion-button>
    </div>

    @if (smartNotes.length === 0) {
      <div class="empty-state">
        <ion-icon name="document-text-outline" class="empty-icon"></ion-icon>
        <ion-text class="empty-text">
          {{ 'training.no-smart-notes' | translate }}
        </ion-text>
        <ion-text class="empty-subtitle">
          {{ 'training.add-first-smart-note' | translate }}
        </ion-text>
      </div>
    } @else {
      <div class="notes-list">
        @for (smartNote of smartNotes; track smartNote.id) {
          <div class="note-item">
            <div class="note-header">
              <ion-text class="note-creator">{{ smartNote.creatorName }}</ion-text>
              <ion-text class="note-date">{{ smartNote.createdOn | date:'short' }}</ion-text>
            </div>
            <div class="note-content">
              <ion-text>{{ smartNote.content }}</ion-text>
            </div>
            @if (smartNote.mediaUrl) {
              <div class="note-media">
                <img [src]="smartNote.mediaUrl" alt="Smart note media" />
              </div>
            }
            <div class="note-actions">
              <ion-button 
                fill="clear" 
                size="small"
                (click)="handleEditSmartNote(smartNote)"
              >
                <ion-icon name="create-outline"></ion-icon>
              </ion-button>
              <ion-button 
                fill="clear" 
                size="small" 
                color="danger"
                (click)="handleDeleteSmartNote(smartNote)"
              >
                <ion-icon name="trash-outline"></ion-icon>
              </ion-button>
            </div>
          </div>
        }
      </div>
    }
  </div>
</mpg-modal-layout>
