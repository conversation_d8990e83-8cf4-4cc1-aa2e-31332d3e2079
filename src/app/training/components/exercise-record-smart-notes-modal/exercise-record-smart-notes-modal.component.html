<mpg-modal-layout-v2
  [title]="title"
  [subtitle]="subtitle"
  [scrollY]="false"
  [showFooter]="true"
>
  <!-- Chat Messages Container -->
  <div class="chat-container">
    <div class="messages-list">
      @for (message of messages; track message.id) {
        <div class="message-wrapper" [class.current-user]="message.isCurrentUser">
          <div class="message-bubble" [class.current-user]="message.isCurrentUser">

            <!-- Text Message -->
            @if (message.type === 'text') {
              <div class="text-message">
                <ion-text>{{ message.content }}</ion-text>
              </div>
            }

            <!-- Voice Message -->
            @if (message.type === 'voice') {
              <div class="voice-message" (click)="handlePlayVoice(message)">
                <ion-button fill="clear" class="play-button">
                  <ion-icon name="play"></ion-icon>
                </ion-button>
                <div class="voice-waveform">
                  <div class="waveform-bars">
                    @for (bar of [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]; track bar) {
                      <div class="bar" [style.height.px]="Math.random() * 20 + 5"></div>
                    }
                  </div>
                </div>
                <ion-text class="duration">{{ formatDuration(message.duration) }}</ion-text>
              </div>
              @if (message.content) {
                <div class="voice-transcript">
                  <ion-text>{{ message.content }}</ion-text>
                </div>
              }
            }

            <!-- Media Message -->
            @if (message.type === 'media') {
              <div class="media-message" (click)="handlePreviewMedia(message)">
                <div class="media-thumbnail">
                  <img [src]="message.mediaUrl" alt="Media content" />
                  <div class="media-overlay">
                    <ion-button fill="clear" class="preview-button">
                      <ion-icon name="play"></ion-icon>
                    </ion-button>
                    <ion-text class="preview-text">Preview</ion-text>
                  </div>
                </div>
              </div>
            }

            <!-- Message Info -->
            <div class="message-info">
              <ion-text class="sender-name">@{{ message.senderName }}</ion-text>
              <ion-text class="timestamp">{{ message.timestamp }}</ion-text>
            </div>
          </div>
        </div>
      }
    </div>
  </div>

  <!-- Message Input Footer -->
  <div slot="footer" class="message-input-container">
    <div class="input-wrapper">
      <ion-button
        fill="clear"
        class="attachment-button"
        (click)="handleAttachment()"
      >
        <ion-icon name="attach"></ion-icon>
      </ion-button>

      <ion-input
        #messageInput
        [(ngModel)]="newMessage"
        placeholder="Write a message"
        class="message-input"
        (keydown.enter)="handleSendMessage()"
      ></ion-input>

      <ion-button
        fill="clear"
        class="voice-button"
        [class.recording]="isRecording"
        (click)="handleVoiceRecord()"
      >
        <ion-icon [name]="isRecording ? 'stop' : 'mic'"></ion-icon>
      </ion-button>
    </div>
  </div>
</mpg-modal-layout-v2>
