<mpg-modal-layout-v2
  [scrollY]="false"
  [showFooter]="true"
  [subtitle]="subtitle"
  [title]="title"
>
  <!-- Chat Messages Container -->
  <div class="chat-container">
    <div class="messages-list">
      @for (message of messages; track message.id) {
        <div class="message-wrapper" [class.current-user]="message.isCurrentUser">

          <!-- Message Info -->
          <div class="message-info" [class.right-align]="message.isCurrentUser" [class.current-user]="message.isCurrentUser">
            <ion-text class="sender-name">&#64;{{ message.senderName }}</ion-text>
            <ion-text class="timestamp">{{ message.timestamp }}</ion-text>
          </div>

          <!-- Media Message (Video Thumbnail) -->
          @if (message.type === 'media') {
            <div class="media-message" (click)="handlePreviewMedia(message)">
              <div class="media-thumbnail">
                <div class="play-overlay">
                  <div class="play-icon">
                    <!-- Play icon SVG from Figma -->
                    <svg width="15" height="14" viewBox="0 0 15 14" fill="none">
                      <g clip-path="url(#clip0_3135_5318)">
                        <path d="M4.45825 2.3333V11.6666L12.0416 6.99996L4.45825 2.3333Z" fill="#F5F5FF"/>
                      </g>
                      <defs>
                        <clipPath id="clip0_3135_5318">
                          <rect width="14" height="14" fill="white" transform="translate(0.375)"/>
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          }

          <!-- Voice Message -->
          @if (message.type === 'voice') {
            <div class="voice-message" (click)="handlePlayVoice(message)">
              <div class="play-button">
                <div class="play-icon">
                  <!-- Play icon SVG from Figma -->
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                    <g clip-path="url(#clip0_3135_5322)">
                      <path d="M4.08337 2.33328V11.6666L11.6667 6.99995L4.08337 2.33328Z" fill="#1F1F1F"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_3135_5322">
                        <rect width="14" height="14" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                </div>
                <!-- Recording dot -->
                <div class="recording-dot" style="background: rgba(243, 243, 255, 1);"></div>
              </div>
              <!-- Waveform visualization -->
              <div class="waveform">
                <!-- Add waveform bars here -->
              </div>
              <ion-text class="duration">{{ formatDuration(message.duration || 0) }}</ion-text>
            </div>
          }

          <!-- Text Message -->
          @if (message.type === 'text') {
            <div class="text-message">
              <ion-text>{{ message.content }}</ion-text>
            </div>
          }

        </div>
      }
    </div>
  </div>

  <!-- Message Input Footer -->
  <div class="message-input-container" slot="footer">
    <div class="input-wrapper">
      <ion-button
        (click)="handleAttachment()"
        class="attachment-button"
        fill="clear"
      >
        <ion-icon name="attach"></ion-icon>
      </ion-button>

      <ion-input
        #messageInput
        (keydown.enter)="handleSendMessage()"
        [(ngModel)]="newMessage"
        class="message-input"
        placeholder="Write a message"
      ></ion-input>

      <ion-button
        (click)="handleVoiceRecord()"
        [class.recording]="isRecording"
        class="voice-button"
        fill="clear"
      >
        <ion-icon [name]="isRecording ? 'stop' : 'mic'"></ion-icon>
      </ion-button>
    </div>
  </div>
</mpg-modal-layout-v2>
