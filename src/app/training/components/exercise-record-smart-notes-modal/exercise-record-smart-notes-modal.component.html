<mpg-modal-layout-v2
  [scrollY]="false"
  [showFooter]="true"
  [subtitle]="subtitle"
  [title]="title"
>
  <!-- Chat Messages Container -->
  <div class="chat-container">
    <div class="messages-list">
      @if (smartNotes.length === 0) {
        <div class="empty-state">
          <ion-icon name="chatbubbles-outline" class="empty-icon"></ion-icon>
          <ion-text class="empty-text">No smart notes yet</ion-text>
          <ion-text class="empty-subtitle">Start a conversation about this exercise</ion-text>
        </div>
      } @else {
        @for (smartNote of smartNotes; track smartNote.id) {
          <div class="message-wrapper" [class.current-user]="isCurrentUser(smartNote)">

            <!-- Message Info -->
            <div class="message-info" [class.right-align]="isCurrentUser(smartNote)" [class.current-user]="isCurrentUser(smartNote)">
              <ion-text class="sender-name">&#64;{{ smartNote.creatorName }}</ion-text>
              <ion-text class="timestamp">{{ formatTimestamp(smartNote.createdOn) }}</ion-text>
            </div>

            <!-- Media Message (Photo/Video) -->
            @if (getSmartNoteDisplayType(smartNote) === 'media') {
              <div class="media-message" (click)="handlePreviewMedia(smartNote)">
                <div class="media-thumbnail">
                  @if (smartNote.mediaUrl) {
                    <img [src]="smartNote.mediaUrl" alt="Smart note media" />
                  }
                  <div class="play-overlay">
                    <div class="play-icon">
                      <!-- Play icon SVG from Figma -->
                      <svg width="15" height="14" viewBox="0 0 15 14" fill="none">
                        <g clip-path="url(#clip0_3135_5318)">
                          <path d="M4.45825 2.3333V11.6666L12.0416 6.99996L4.45825 2.3333Z" fill="#F5F5FF"/>
                        </g>
                        <defs>
                          <clipPath id="clip0_3135_5318">
                            <rect width="14" height="14" fill="white" transform="translate(0.375)"/>
                          </clipPath>
                        </defs>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            }

            <!-- Voice Message (Audio) -->
            @if (getSmartNoteDisplayType(smartNote) === 'voice') {
              <div class="voice-message" (click)="handlePlayVoice(smartNote)">
                <div class="play-button">
                  <div class="play-icon">
                    <!-- Play icon SVG from Figma -->
                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                      <g clip-path="url(#clip0_3135_5322)">
                        <path d="M4.08337 2.33328V11.6666L11.6667 6.99995L4.08337 2.33328Z" fill="#1F1F1F"/>
                      </g>
                      <defs>
                        <clipPath id="clip0_3135_5322">
                          <rect width="14" height="14" fill="white"/>
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <!-- Recording dot -->
                  <div class="recording-dot" style="background: rgba(243, 243, 255, 1);"></div>
                </div>
                <!-- Waveform visualization -->
                <div class="waveform">
                  <!-- Add waveform bars here -->
                </div>
              </div>
              @if (smartNote.content) {
                <div class="voice-transcript">
                  <ion-text>{{ smartNote.content }}</ion-text>
                </div>
              }
            }

            <!-- Text Message -->
            @if (getSmartNoteDisplayType(smartNote) === 'text') {
              <div class="text-message">
                <ion-text>{{ smartNote.content }}</ion-text>
              </div>
            }

          </div>
        }
      }
    </div>
  </div>

  <!-- Message Input Footer -->
  <div class="message-input-container" slot="footer">
    <div class="input-wrapper">
      <ion-button
        (click)="handleAttachment()"
        class="attachment-button"
        fill="clear"
      >
        <ion-icon name="attach"></ion-icon>
      </ion-button>

      <ion-input
        #messageInput
        (keydown.enter)="handleSendMessage()"
        [(ngModel)]="newMessage"
        class="message-input"
        placeholder="Write a message"
      ></ion-input>

      <ion-button
        (click)="handleVoiceRecord()"
        [class.recording]="isRecording"
        class="voice-button"
        fill="clear"
      >
        <ion-icon [name]="isRecording ? 'stop' : 'mic'"></ion-icon>
      </ion-button>
    </div>
  </div>
</mpg-modal-layout-v2>
