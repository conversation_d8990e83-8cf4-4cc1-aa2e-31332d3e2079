import { Component, Input, OnInit, ViewChild, ElementRef } from '@angular/core';
import { WorkoutExerciseRecord, WorkoutExerciseSetRecord } from '../../models';
import { ModalService, SmartNoteService } from '../../../shared/services';
import { SmartNote, SmartNoteCreateRequest, SmartNoteType } from '../../../shared/models';
import { WorkoutExerciseSetRecordService } from '../../services';
import { Observable, forkJoin } from 'rxjs';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss'],
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;
  @ViewChild('messageInput') messageInput: ElementRef;

  messages: ChatMessage[] = [];
  newMessage = '';
  isRecording = false;
  isLoading = false;
  Math = Math; // Make Math available in template

  constructor(
    private modalService: ModalService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
  ) {}

  get title(): string {
    return this.exerciseRecord?.exercise?.exercise?.name || 'Exercise Name';
  }

  get subtitle(): string {
    const setCount = this.exerciseRecord?.setRecords?.length || 0;
    return Array.from({ length: setCount }, (_, i) => i + 1).join('  ');
  }

  ngOnInit(): void {
    this.loadMessages();
  }

  private loadMessages(): void {
    // Mock data to demonstrate the chat interface
    this.messages = [
      {
        id: '1',
        type: 'voice',
        content: 'Try more, hold onto it',
        duration: 2.02,
        timestamp: 'Today 11:52',
        senderName: 'Zori',
        senderId: 'zori',
        isCurrentUser: false,
      },
      {
        id: '2',
        type: 'media',
        content: '',
        mediaUrl: '/assets/placeholder-video.jpg',
        timestamp: 'Today 11:52',
        senderName: 'Zori',
        senderId: 'zori',
        isCurrentUser: false,
      },
      {
        id: '3',
        type: 'text',
        content: 'Giving it all',
        timestamp: 'Today 11:52',
        senderName: 'Aleksandar',
        senderId: 'aleksandar',
        isCurrentUser: true,
      },
      {
        id: '4',
        type: 'text',
        content: 'Keep it up',
        timestamp: 'Today 11:52',
        senderName: 'Aleksandar',
        senderId: 'aleksandar',
        isCurrentUser: true,
      },
    ];
  }

  handleSendMessage(): void {
    if (!this.newMessage.trim()) return;

    const message: ChatMessage = {
      id: Date.now().toString(),
      type: 'text',
      content: this.newMessage.trim(),
      timestamp: 'Now',
      senderName: 'You',
      senderId: 'current-user',
      isCurrentUser: true,
    };

    this.messages.push(message);
    this.newMessage = '';
  }

  handleVoiceRecord(): void {
    this.isRecording = !this.isRecording;
    // Implement voice recording logic here
    console.log('Voice recording:', this.isRecording ? 'started' : 'stopped');
  }

  handleAttachment(): void {
    // Implement file attachment logic here
    console.log('Handle attachment');
  }

  handlePlayVoice(message: ChatMessage): void {
    console.log('Play voice message:', message.id);
  }

  handlePreviewMedia(message: ChatMessage): void {
    console.log('Preview media:', message.id);
  }

  formatDuration(duration: number): string {
    return duration.toFixed(2);
  }
}
