import { Component, Input, OnInit } from '@angular/core';
import { WorkoutExerciseRecord } from '../../models';
import { ModalService } from '../../../shared/services';
import { Button } from '../../../shared/models';
import { SmartNote, SmartNoteCreateRequest } from '../../../shared/models';
import { WorkoutExerciseSetRecordService } from '../../services';
import { Observable } from 'rxjs';

@Component({
  selector: 'mpg-exercise-record-smart-notes-modal',
  templateUrl: './exercise-record-smart-notes-modal.component.html',
  styleUrls: ['./exercise-record-smart-notes-modal.component.scss'],
})
export class ExerciseRecordSmartNotesModalComponent implements OnInit {
  @Input() exerciseRecord: WorkoutExerciseRecord;

  smartNotes: SmartNote[] = [];
  isLoading = false;

  constructor(
    private modalService: ModalService,
    private workoutExerciseSetRecordService: WorkoutExerciseSetRecordService,
  ) {}

  get modalButton(): Button {
    return {
      label: 'buttons.close',
      handler: () => {
        this.modalService.closeTopModal();
      },
    };
  }

  get title(): string {
    return `Smart Notes - ${this.exerciseRecord.exercise.exercise.name}`;
  }

  ngOnInit(): void {
    this.loadSmartNotes();
  }

  private loadSmartNotes(): void {
    // For now, we'll initialize with empty array
    // In a real implementation, you would fetch smart notes from the backend
    this.smartNotes = [];
  }

  handleCreateSmartNote(): void {
    // This would open a modal to create a new smart note
    // For now, we'll just add a placeholder
    console.log('Create smart note for exercise record:', this.exerciseRecord.id);
  }

  handleEditSmartNote(smartNote: SmartNote): void {
    console.log('Edit smart note:', smartNote.id);
  }

  handleDeleteSmartNote(smartNote: SmartNote): void {
    console.log('Delete smart note:', smartNote.id);
  }
}
