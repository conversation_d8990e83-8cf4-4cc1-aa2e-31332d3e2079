import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

import { ExerciseRecordSmartNotesModalComponent } from './exercise-record-smart-notes-modal.component';
import { ModalLayoutModule } from '../../../shared/components/modal-layout/modal-layout.module';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    TranslateModule,
    ModalLayoutModule,
  ],
  declarations: [ExerciseRecordSmartNotesModalComponent],
  exports: [ExerciseRecordSmartNotesModalComponent],
})
export class ExerciseRecordSmartNotesModalModule {}
